<?php

namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\Driver;
use App\Models\Task;
use App\Models\Teams;
use App\Models\User;
use App\Services\ReportService;
use App\Exports\CustomerTasksExport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;

class PlatformReportsController extends Controller
{
    protected $reportService;

    public function __construct(ReportService $reportService)
    {
        $this->reportService = $reportService;
    }


    public function index()
    {
        try {
            return view('admin.reports.index');
        } catch (\Exception $e) {
            return response('Error: ' . $e->getMessage(), 500);
        }
    }


    /**
    * View customer tasks report page
    */
    public function customerReport()
    {

        // Get required data for the view
        $customers = Customer::select('id', 'name', 'company_name')->get();
        $drivers = Driver::select('id', 'name', 'phone')->get();
        $teams = Teams::select('id', 'name')->get();

        $taskStatuses = [
            'in_progress' => 'in_progress',
            'advertised' => 'advertised',
            'assign' => 'assign',
            'started' => 'started',
            'in pickup point' => 'in pickup point',
            'loading' => 'loading',
            'in the way' => 'in the way',
            'in delivery point' => 'in delivery point',
            'unloading' => 'unloading',
            'completed' => 'completed',
            'canceled' => 'canceled',
        ];

        $paymentStatuses = [
             'waiting' => 'waiting',
            'completed' => 'completed',
            'pending' => 'pending'
        ];

        $paymentMethods = [
            'bank_transfer' => 'bank transfer',
            'credit_card' => 'credit card',
            'wallet' => 'wallet'
        ];

        return view('admin.reports.customer-tasks', compact(
            'customers',
            'drivers',
            'teams',
            'taskStatuses',
            'paymentStatuses',
            'paymentMethods'
        ));
    }

    /**
     * Generate customer tasks report data
     */
    public function generateCustomerTasksReport(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'customer_ids' => 'required|array|min:1',
                'customer_ids.*' => 'exists:customers,id',
                'date_from' => 'required|date',
                'date_to' => 'required|date|after_or_equal:date_from',
                'columns' => 'required|array|min:4',
                'export_type' => 'required|in:excel,pdf'
            ]);

            // Generate report data
            $reportData = $this->reportService->generateCustomerTasksReport($request->all());

            if ($request->export_type === 'excel') {
                return $this->exportToExcel($reportData, $request->all());
            } else {
                return $this->exportToPdf($reportData, $request->all());
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء التقرير: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export report to Excel
     */
    private function exportToExcel($reportData, $filters)
    {
        $filename = 'customer_tasks_report_' . date('Y-m-d_H-i-s') . '.xlsx';

        return Excel::download(
            new CustomerTasksExport($reportData, $filters),
            $filename
        );
    }

    /**
     * Export report to PDF (using browser print)
     */
    private function exportToPdf($reportData, $filters)
    {
        // Get customer names for the report
        $customerNames = Customer::whereIn('id', $filters['customer_ids'])
            ->get();

        return view('admin.reports.pdf.customer-tasks-simple', compact(
            'reportData',
            'filters',
            'customerNames'
        ));
    }

    /**
     * Get report preview data (for table display)
     */
    public function getReportPreview(Request $request)
    {
        try {
            $request->validate([
                'customer_ids' => 'required|array|min:1',
                'customer_ids.*' => 'exists:customers,id',
                'date_from' => 'required|date',
                'date_to' => 'required|date|after_or_equal:date_from'
            ]);

            $reportData = $this->reportService->generateCustomerTasksReport($request->all(), true); // Preview mode

            return response()->json([
                'success' => true,
                'data' => $reportData['tasks'],
                'summary' => $reportData['summary']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage()
            ], 500);
        }
    }



    /**
       * View Driver tasks report page
       */
    public function driverReport()
    {
        $customers = Customer::select('id', 'name', 'company_name')->get();
        $drivers = Driver::with('team:id,name')->select('id', 'name', 'phone', 'team_id')->get();
        $teams = Teams::select('id', 'name')->get();

        $taskStatuses = [
            'pending' => __('Pending'),
            'confirmed' => __('Confirmed'),
            'in_progress' => __('In Progress'),
            'completed' => __('Completed'),
            'canceled' => __('Canceled'),
            'refund' => __('Refund')
        ];

        $paymentStatuses = [
            'pending' => __('Pending'),
            'completed' => __('Completed'),
            'waiting' => __('Waiting')
        ];

        $paymentMethods = [
            'cash' => 'cash',
            'bank_transfer' => 'bank transfer',
            'credit_card' => 'credit card',
            'wallet' => 'wallet'
        ];

        return view('admin.reports.driver-tasks', compact(
            'customers',
            'drivers',
            'teams',
            'taskStatuses',
            'paymentStatuses',
            'paymentMethods'
        ));
    }
    /**
     * Generate driver tasks report data
     */
    public function generateDriverTasksReport(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'driver_ids' => 'required|array|min:1',
                'driver_ids.*' => 'exists:drivers,id',
                'date_from' => 'required|date',
                'date_to' => 'required|date|after_or_equal:date_from',
                'format' => 'required|in:excel,pdf'
            ]);

            // Generate report data
            $reportData = $this->reportService->generateDriverTasksReport($request->all());

            if ($request->format === 'excel') {
                return $this->exportDriverTasksToExcel($reportData, $request->all());
            } else {
                return $this->exportDriverTasksToPdf($reportData, $request->all());
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء التقرير: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get driver tasks report preview
     */
    public function getDriverTasksPreview(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'driver_ids' => 'required|array|min:1',
                'driver_ids.*' => 'exists:drivers,id',
                'date_from' => 'required|date',
                'date_to' => 'required|date|after_or_equal:date_from'
            ]);

            $reportData = $this->reportService->generateDriverTasksReport($request->all(), true); // Preview mode

            return response()->json([
                'success' => true,
                'data' => [
                    'tasks' => $reportData['tasks'],
                    'summary' => $reportData['summary']
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate team tasks report data
     */
    public function generateTeamTasksReport(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'team_ids' => 'required|array|min:1',
                'team_ids.*' => 'exists:teams,id',
                'date_from' => 'required|date',
                'date_to' => 'required|date|after_or_equal:date_from',
                'format' => 'required|in:excel,pdf'
            ]);

            // Generate report data
            $reportData = $this->reportService->generateTeamTasksReport($request->all());

            if ($request->format === 'excel') {
                return $this->exportTeamTasksToExcel($reportData, $request->all());
            } else {
                return $this->exportTeamTasksToPdf($reportData, $request->all());
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء التقرير: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get team tasks report preview
     */
    public function getTeamTasksPreview(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'team_ids' => 'required|array|min:1',
                'team_ids.*' => 'exists:teams,id',
                'date_from' => 'required|date',
                'date_to' => 'required|date|after_or_equal:date_from'
            ]);

            $reportData = $this->reportService->generateTeamTasksReport($request->all(), true); // Preview mode

            return response()->json([
                'success' => true,
                'data' => [
                    'tasks' => $reportData['tasks'],
                    'summary' => $reportData['summary']
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export driver tasks to Excel
     */
    private function exportDriverTasksToExcel($reportData, $filters)
    {
        // Implementation similar to customer tasks Excel export
        // This will be handled by the ReportService
        return $this->reportService->exportDriverTasksToExcel($reportData, $filters);
    }

    /**
     * Export driver tasks to PDF
     */
    private function exportDriverTasksToPdf($reportData, $filters)
    {
        // Implementation similar to customer tasks PDF export
        return $this->reportService->exportDriverTasksToPdf($reportData, $filters);
    }

    /**
     * Export team tasks to Excel
     */
    private function exportTeamTasksToExcel($reportData, $filters)
    {
        // Implementation similar to customer tasks Excel export
        return $this->reportService->exportTeamTasksToExcel($reportData, $filters);
    }

    /**
     * Export team tasks to PDF
     */
    private function exportTeamTasksToPdf($reportData, $filters)
    {
        // Implementation similar to customer tasks PDF export
        return $this->reportService->exportTeamTasksToPdf($reportData, $filters);
    }
}
