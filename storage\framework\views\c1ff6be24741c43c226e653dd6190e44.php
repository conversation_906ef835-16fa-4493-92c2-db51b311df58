<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e(__('Driver Tasks Report - Simple')); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #2c3e50;
            padding-bottom: 20px;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .report-title {
            font-size: 18px;
            color: #34495e;
            margin-bottom: 10px;
        }

        .report-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .info-row:last-child {
            margin-bottom: 0;
        }

        .info-label {
            font-weight: bold;
            color: #495057;
        }

        .info-value {
            color: #6c757d;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
        }

        .data-table th,
        .data-table td {
            border: 1px solid #dee2e6;
            padding: 8px 6px;
            text-align: center;
            vertical-align: middle;
        }

        .data-table th {
            background: #2c3e50;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }

        .data-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }

        .data-table tbody tr:hover {
            background: #e9ecef;
        }

        .status-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }

        .status-completed {
            background: #28a745;
        }

        .status-in-progress {
            background: #007bff;
        }

        .status-canceled {
            background: #dc3545;
        }

        .status-pending {
            background: #ffc107;
            color: #212529;
        }

        .summary {
            background: #e8f5e8;
            border: 1px solid #28a745;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }

        .summary-title {
            font-size: 16px;
            font-weight: bold;
            color: #155724;
            margin-bottom: 10px;
            text-align: center;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        .summary-item {
            text-align: center;
        }

        .summary-label {
            font-weight: bold;
            color: #155724;
            font-size: 11px;
        }

        .summary-value {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
        }

        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }

        .page-break {
            page-break-before: always;
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .container {
                padding: 10px;
            }

            .data-table {
                font-size: 10px;
            }

            .data-table th,
            .data-table td {
                padding: 5px 3px;
            }
        }

        @page {
            size: A4 landscape;
            margin: 1cm;
        }

        .text-truncate {
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .small-text {
            font-size: 10px;
            color: #6c757d;
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <span class="app-brand-logo demo"><?php echo $__env->make('_partials.macros', ['height' => 20], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?></span>
            <div class="company-name"><?php echo e(__('SafeDests Transport and Logistics Company')); ?></div>
            <div class="report-title"><?php echo e(__('Driver Tasks Report')); ?></div>
        </div>

        <!-- Report Information -->
        <div class="report-info">
            <div class="info-row">
                <span class="info-label">السائق:</span>

                <?php $__currentLoopData = $driverNames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $driver): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <span class="info-value">
                        <?php echo e($driver->name); ?> - <?php echo e($driver->phone); ?>

                    </span>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            </div>
            <div class="info-row">
                <span class="info-label">الفترة الزمنية:</span>
                <span class="info-value"><?php echo e($filters['date_from']); ?> إلى <?php echo e($filters['date_to']); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">تاريخ إنشاء التقرير:</span>
                <span class="info-value"><?php echo e($reportData['generated_at']->format('Y-m-d H:i:s')); ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">أنشئ بواسطة:</span>
                <span class="info-value"><?php echo e($reportData['generated_by']); ?></span>
            </div>
            <?php if(!empty($reportData['filters_applied'])): ?>
                <div class="info-row">
                    <span class="info-label">الفلاتر المطبقة:</span>
                    <span class="info-value">
                        <?php $__currentLoopData = $reportData['filters_applied']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($value): ?>
                                <?php echo e($key); ?>: <?php echo e($value); ?><?php echo e(!$loop->last ? ' | ' : ''); ?>

                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </span>
                </div>
            <?php endif; ?>
        </div>

        <!-- Data Table -->
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 6%"><?php echo e(__('Task ID')); ?></th>
                    <th style="width: 10%"><?php echo e(__('Task Price')); ?></th>
                    <th style="width: 20%"><?php echo e(__('Route')); ?></th>
                    <th style="width: 15%"><?php echo e(__('Customer')); ?></th>
                    <th style="width: 10%"><?php echo e(__('Task Status')); ?></th>
                    <th style="width: 10%"><?php echo e(__('Payment Status')); ?></th>
                    <th style="width: 10%"><?php echo e(__('Payment Method')); ?></th>
                    <th style="width: 10%"><?php echo e(__('Created Date')); ?></th>
                    <th style="width: 9%"><?php echo e(__('Closed Date')); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $reportData['tasks']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td><?php echo e($task['id']); ?></td>
                        <td>
                            <?php
                                $displayPrice = $task['total_price'] - ($task['commission'] ?? 0);
                            ?>
                            <?php if($displayPrice == 0 && isset($task['original_price']) && $task['original_price'] > 0): ?>
                                <span
                                    style="text-decoration: line-through; color: #6c757d;"><?php echo e(number_format($task['original_price'], 2)); ?></span>
                                <br><strong style="color: #dc3545;">0.00</strong> <?php echo e(__('SAR')); ?>

                                <br><small style="color: #dc3545;"><?php echo e(__('Refunded/Cancelled')); ?></small>
                            <?php else: ?>
                                <?php echo e(number_format($displayPrice, 2)); ?> <?php echo e(__('SAR')); ?>

                            <?php endif; ?>
                        </td>
                        <td class="text-truncate">
                            <strong><?php echo e(__('From')); ?>:</strong> <?php echo e($task['pickup']['address']); ?><br>
                            <strong><?php echo e(__('To')); ?>:</strong> <?php echo e($task['delivery']['address']); ?>

                        </td>
                        <td>
                            <strong><?php echo e($task['customer']['name']); ?></strong><br>
                            <?php if($task['customer']['company_name']): ?>
                                <span class="small-text"><?php echo e($task['customer']['company_name']); ?></span><br>
                            <?php endif; ?>
                            <span class="small-text"><?php echo e($task['customer']['phone'] ?? ''); ?></span>
                        </td>
                        <td>
                            <?php
                                $statusClass = 'status-pending';
                                if (in_array($task['status'], ['completed', 'invoiced'])) {
                                    $statusClass = 'status-completed';
                                } elseif (in_array($task['status'], ['in_progress', 'started', 'in the way'])) {
                                    $statusClass = 'status-in-progress';
                                } elseif (in_array($task['status'], ['canceled', 'refund'])) {
                                    $statusClass = 'status-canceled';
                                }
                            ?>
                            <span class="status-badge <?php echo e($statusClass); ?>"><?php echo e($task['status']); ?></span>
                        </td>
                        <td>
                            <?php
                                $paymentStatusClass = 'status-pending';
                                if ($task['payment_status'] === 'completed') {
                                    $paymentStatusClass = 'status-completed';
                                } elseif ($task['payment_status'] === 'pending') {
                                    $paymentStatusClass = 'status-in-progress';
                                } elseif ($task['payment_status'] === 'waiting') {
                                    $paymentStatusClass = 'status-canceled';
                                }
                            ?>
                            <span class="status-badge <?php echo e($paymentStatusClass); ?>"><?php echo e($task['payment_status']); ?></span>
                        </td>
                        <td>
                            <?php if($task['payment_status'] === 'completed'): ?>
                                <?php if(empty($task['payment_method'])): ?>
                                    <span style="color: #6c757d; font-style: italic;"><?php echo e(__('Not Completed')); ?></span>
                                <?php else: ?>
                                    <?php echo e($task['payment_method']); ?>

                                <?php endif; ?>
                            <?php endif; ?>

                        </td>
                        <td><?php echo e($task['created_at']); ?></td>
                        <td><?php echo e($task['closed_at'] ?: __('Not closed yet')); ?></td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 20px; color: #6c757d;">
                            <?php echo e(__('No tasks match the specified criteria')); ?>

                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>

        <!-- Summary -->
        <?php if(!empty($reportData['summary'])): ?>
            <div class="summary">
                <div class="summary-title"><?php echo e(__('Report Summary')); ?></div>

                <!-- Basic Summary -->
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-label"><?php echo e(__('Total Tasks')); ?></div>
                        <div class="summary-value"><?php echo e($reportData['summary']['total_tasks']); ?></div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label"><?php echo e(__('Total Amount (After Commission)')); ?></div>
                        <div class="summary-value"><?php echo e(number_format($reportData['summary']['total_amount'], 2)); ?>

                            <?php echo e(__('SAR')); ?></div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label"><?php echo e(__('Average Task Price')); ?></div>
                        <div class="summary-value"><?php echo e(number_format($reportData['summary']['average_amount'], 2)); ?>

                            <?php echo e(__('SAR')); ?></div>
                    </div>
                </div>




            </div>
        <?php endif; ?>

        <!-- Footer -->
        <div class="footer">
            <p>تم إنشاء هذا التقرير تلقائياً بواسطة نظام SafeDests للنقل والخدمات اللوجستية</p>
            <p>للاستفسارات والدعم الفني، يرجى التواصل مع فريق الدعم</p>
        </div>
    </div>

    <!-- Print Script -->
    <script>
        // Auto print when page loads
        window.onload = function() {
            // Small delay to ensure styles are loaded
            setTimeout(function() {
                window.print();
            }, 500);
        };

        // Close window after printing
        window.onafterprint = function() {
            setTimeout(function() {
                window.close();
            }, 1000);
        };
    </script>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\safedestssss\resources\views/admin/reports/pdf/driver-tasks-simple.blade.php ENDPATH**/ ?>